"""
Adapters package for the Trigger Service.

This package contains the adapter/connector pattern implementation for
different trigger types. Each adapter implements the BaseTriggerAdapter
interface to provide consistent trigger functionality.
"""

from .base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerStatus,
    TriggerConfiguration,
    AdapterHealthStatus,
)

__all__ = [
    "BaseTriggerAdapter",
    "TriggerEvent",
    "TriggerEventType",
    "TriggerStatus",
    "TriggerConfiguration",
    "AdapterHealthStatus",
]
