"""
Base adapter interface for trigger implementations.

This module defines the abstract base class that all trigger adapters must
implement to ensure consistent behavior across different trigger types.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel


class TriggerEvent(BaseModel):
    """
    Standardized trigger event data structure.
    
    All adapters must transform their specific event formats into this
    standardized structure for consistent processing.
    """
    event_id: str
    event_type: str  # 'created', 'updated', 'deleted', 'reminder'
    source: str  # 'google_calendar', 'slack', etc.
    timestamp: str  # ISO format timestamp
    data: Dict[str, Any]  # Adapter-specific event data
    metadata: Optional[Dict[str, Any]] = None


class BaseTriggerAdapter(ABC):
    """
    Abstract base class for all trigger adapters.
    
    This class defines the interface that all trigger adapters must implement
    to provide consistent trigger functionality across different services.
    """
    
    def __init__(self, adapter_name: str):
        """
        Initialize the base adapter.
        
        Args:
            adapter_name: Unique name for this adapter type
        """
        self.adapter_name = adapter_name
    
    @abstractmethod
    async def setup_trigger(
        self,
        trigger_id: UUID,
        trigger_config: Dict[str, Any],
        event_types: List[str]
    ) -> bool:
        """
        Set up a new trigger with the external service.
        
        Args:
            trigger_id: Unique identifier for the trigger
            trigger_config: Adapter-specific configuration
            event_types: List of event types to monitor
            
        Returns:
            bool: True if setup was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a trigger from the external service.
        
        Args:
            trigger_id: Unique identifier for the trigger to remove
            
        Returns:
            bool: True if removal was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def process_event(
        self,
        raw_event: Dict[str, Any]
    ) -> Optional[TriggerEvent]:
        """
        Process a raw event from the external service.
        
        Args:
            raw_event: Raw event data from the external service
            
        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        pass
    
    @abstractmethod
    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate adapter-specific configuration.
        
        Args:
            config: Configuration to validate
            
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        Check if the adapter and its external service are healthy.
        
        Returns:
            bool: True if healthy, False otherwise
        """
        pass
