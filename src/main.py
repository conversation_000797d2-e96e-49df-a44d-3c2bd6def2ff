"""
Main application entry point for the Trigger Service.

This module initializes and configures the FastAPI application with all
necessary middleware, routes, and startup/shutdown events.
"""

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from src.utils.config import get_settings
from src.utils.logger import setup_logging
from src.database.connection import init_database, close_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    # Startup
    settings = get_settings()
    setup_logging(settings.log_level, settings.log_format)

    try:
        await init_database()
        yield
    finally:
        # Shutdown
        await close_database()


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    # Create FastAPI app with lifespan
    app = FastAPI(
        title="Trigger Service",
        description="Event-based trigger service for workflow automation",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # TODO: Add middleware registration
    # TODO: Add router registration

    return app


app = create_app()


def main() -> None:
    """Main entry point for the application."""
    settings = get_settings()
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
