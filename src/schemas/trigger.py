"""
Pydantic schemas for trigger-related API operations.

This module contains all Pydantic models for trigger creation, updates,
and responses.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class TriggerCreate(BaseModel):
    """Schema for creating a new trigger."""
    
    user_id: str = Field(..., description="ID of the user creating the trigger")
    workflow_id: str = Field(..., description="ID of the workflow to execute")
    trigger_type: str = Field(..., description="Type of trigger (e.g., 'google_calendar')")
    trigger_name: str = Field(..., description="Human-readable name for the trigger")
    trigger_config: Dict[str, Any] = Field(..., description="Adapter-specific configuration")
    event_types: List[str] = Field(..., description="List of event types to monitor")
    
    class Config:
        schema_extra = {
            "example": {
                "user_id": "user-123",
                "workflow_id": "workflow-456",
                "trigger_type": "google_calendar",
                "trigger_name": "Meeting Reminder Workflow",
                "trigger_config": {
                    "calendar_id": "primary",
                    "event_filters": {
                        "title_contains": ["meeting", "call"],
                        "attendee_count_min": 2
                    }
                },
                "event_types": ["created", "updated"]
            }
        }


class TriggerUpdate(BaseModel):
    """Schema for updating an existing trigger."""
    
    trigger_name: Optional[str] = Field(None, description="Human-readable name for the trigger")
    trigger_config: Optional[Dict[str, Any]] = Field(None, description="Adapter-specific configuration")
    event_types: Optional[List[str]] = Field(None, description="List of event types to monitor")
    is_active: Optional[bool] = Field(None, description="Whether the trigger is active")


class TriggerResponse(BaseModel):
    """Schema for trigger API responses."""
    
    id: UUID = Field(..., description="Unique identifier for the trigger")
    user_id: str = Field(..., description="ID of the user who owns the trigger")
    workflow_id: str = Field(..., description="ID of the associated workflow")
    trigger_type: str = Field(..., description="Type of trigger")
    trigger_name: str = Field(..., description="Human-readable name for the trigger")
    trigger_config: Dict[str, Any] = Field(..., description="Adapter-specific configuration")
    event_types: List[str] = Field(..., description="List of event types being monitored")
    is_active: bool = Field(..., description="Whether the trigger is active")
    created_at: datetime = Field(..., description="When the trigger was created")
    updated_at: datetime = Field(..., description="When the trigger was last updated")
    last_triggered_at: Optional[datetime] = Field(None, description="When the trigger was last activated")
    
    class Config:
        from_attributes = True


class TriggerListResponse(BaseModel):
    """Schema for paginated trigger list responses."""
    
    triggers: List[TriggerResponse] = Field(..., description="List of triggers")
    total: int = Field(..., description="Total number of triggers")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")


class TriggerToggleRequest(BaseModel):
    """Schema for enabling/disabling triggers."""
    
    is_active: bool = Field(..., description="Whether to activate or deactivate the trigger")
