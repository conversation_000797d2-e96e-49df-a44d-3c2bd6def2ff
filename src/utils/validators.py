"""
Validation utilities for the Trigger Service.

This module provides common validation functions for data validation,
input sanitization, and business rule enforcement.
"""

import re
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import ValidationError, validator


def validate_uuid(value: str) -> bool:
    """
    Validate that a string is a valid UUID.
    
    Args:
        value: String to validate
        
    Returns:
        bool: True if valid UUID, False otherwise
    """
    try:
        UUID(value)
        return True
    except (ValueError, TypeError):
        return False


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        bool: True if valid email format, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        bool: True if valid URL format, False otherwise
    """
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return bool(re.match(pattern, url))


def validate_trigger_type(trigger_type: str) -> bool:
    """
    Validate trigger type is supported.
    
    Args:
        trigger_type: Trigger type to validate
        
    Returns:
        bool: True if supported trigger type, False otherwise
    """
    supported_types = {
        "google_calendar",
        "slack",
        "email",
        "github",
        "webhook",
        "schedule"
    }
    return trigger_type in supported_types


def validate_event_types(event_types: List[str], trigger_type: str) -> bool:
    """
    Validate event types for a specific trigger type.
    
    Args:
        event_types: List of event types to validate
        trigger_type: Type of trigger
        
    Returns:
        bool: True if all event types are valid for the trigger type
    """
    valid_events = {
        "google_calendar": {"created", "updated", "deleted", "reminder"},
        "slack": {"message", "reaction", "channel_join", "channel_leave"},
        "email": {"received", "sent"},
        "github": {"push", "pull_request", "issue", "release"},
        "webhook": {"received"},
        "schedule": {"triggered"}
    }
    
    if trigger_type not in valid_events:
        return False
    
    allowed_events = valid_events[trigger_type]
    return all(event_type in allowed_events for event_type in event_types)


def validate_google_calendar_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate Google Calendar trigger configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    errors = []
    
    # Required fields
    if "calendar_id" not in config:
        errors.append("calendar_id is required")
    
    # Optional event filters
    if "event_filters" in config:
        filters = config["event_filters"]
        if not isinstance(filters, dict):
            errors.append("event_filters must be a dictionary")
        else:
            # Validate filter fields
            if "title_contains" in filters:
                if not isinstance(filters["title_contains"], list):
                    errors.append("title_contains must be a list of strings")
                elif not all(isinstance(item, str) for item in filters["title_contains"]):
                    errors.append("title_contains must contain only strings")
            
            if "attendee_count_min" in filters:
                if not isinstance(filters["attendee_count_min"], int) or filters["attendee_count_min"] < 0:
                    errors.append("attendee_count_min must be a non-negative integer")
            
            if "attendee_count_max" in filters:
                if not isinstance(filters["attendee_count_max"], int) or filters["attendee_count_max"] < 0:
                    errors.append("attendee_count_max must be a non-negative integer")
    
    return errors


def validate_slack_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate Slack trigger configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    errors = []
    
    # Required fields
    if "channel_id" not in config and "user_id" not in config:
        errors.append("Either channel_id or user_id is required")
    
    # Validate channel_id format if present
    if "channel_id" in config:
        channel_id = config["channel_id"]
        if not isinstance(channel_id, str) or not channel_id.startswith(("C", "G", "D")):
            errors.append("channel_id must be a valid Slack channel ID")
    
    # Validate user_id format if present
    if "user_id" in config:
        user_id = config["user_id"]
        if not isinstance(user_id, str) or not user_id.startswith("U"):
            errors.append("user_id must be a valid Slack user ID")
    
    return errors


def validate_webhook_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate webhook trigger configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    errors = []
    
    # Optional webhook secret
    if "secret" in config:
        secret = config["secret"]
        if not isinstance(secret, str) or len(secret) < 8:
            errors.append("webhook secret must be at least 8 characters long")
    
    # Optional headers validation
    if "required_headers" in config:
        headers = config["required_headers"]
        if not isinstance(headers, dict):
            errors.append("required_headers must be a dictionary")
        elif not all(isinstance(k, str) and isinstance(v, str) for k, v in headers.items()):
            errors.append("required_headers must contain only string key-value pairs")
    
    return errors


def validate_trigger_config(trigger_type: str, config: Dict[str, Any]) -> List[str]:
    """
    Validate trigger configuration based on trigger type.
    
    Args:
        trigger_type: Type of trigger
        config: Configuration dictionary to validate
        
    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    validators = {
        "google_calendar": validate_google_calendar_config,
        "slack": validate_slack_config,
        "webhook": validate_webhook_config,
    }
    
    validator_func = validators.get(trigger_type)
    if validator_func:
        return validator_func(config)
    
    # For unsupported trigger types, just check it's a dict
    if not isinstance(config, dict):
        return ["Configuration must be a dictionary"]
    
    return []


def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize string input by removing dangerous characters.
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        
    Returns:
        str: Sanitized string
    """
    if not isinstance(value, str):
        return str(value)
    
    # Remove null bytes and control characters
    sanitized = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', value)
    
    # Trim whitespace
    sanitized = sanitized.strip()
    
    # Truncate if max_length specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized


def validate_json_structure(data: Any, required_fields: List[str]) -> List[str]:
    """
    Validate JSON data structure has required fields.
    
    Args:
        data: Data to validate
        required_fields: List of required field names
        
    Returns:
        List[str]: List of validation errors (empty if valid)
    """
    errors = []
    
    if not isinstance(data, dict):
        errors.append("Data must be a JSON object")
        return errors
    
    for field in required_fields:
        if field not in data:
            errors.append(f"Required field '{field}' is missing")
        elif data[field] is None:
            errors.append(f"Required field '{field}' cannot be null")
    
    return errors
