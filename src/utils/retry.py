"""
Retry utilities for the Trigger Service.

This module provides retry mechanisms with exponential backoff for
handling transient failures in external service calls.
"""

import asyncio
import random
import time
from functools import wraps
from typing import Any, Callable, List, Optional, Type, Union

from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class RetryableError(Exception):
    """Base class for errors that should trigger a retry."""
    pass


class NonRetryableError(Exception):
    """Base class for errors that should not trigger a retry."""
    pass


def calculate_backoff_delay(
    attempt: int,
    base_delay: float = 1.0,
    backoff_factor: float = 2.0,
    max_delay: float = 300.0,
    jitter: bool = True
) -> float:
    """
    Calculate delay for exponential backoff with optional jitter.
    
    Args:
        attempt: Current attempt number (0-based)
        base_delay: Base delay in seconds
        backoff_factor: Exponential backoff factor
        max_delay: Maximum delay in seconds
        jitter: Whether to add random jitter
        
    Returns:
        float: Delay in seconds
    """
    delay = base_delay * (backoff_factor ** attempt)
    delay = min(delay, max_delay)
    
    if jitter:
        # Add ±25% jitter to prevent thundering herd
        jitter_range = delay * 0.25
        delay += random.uniform(-jitter_range, jitter_range)
        delay = max(0, delay)  # Ensure non-negative
    
    return delay


class RetryHandler:
    """
    Handles retry logic with exponential backoff.
    
    This class provides configurable retry behavior for handling
    transient failures in external service calls.
    """
    
    def __init__(
        self,
        max_attempts: Optional[int] = None,
        base_delay: float = 1.0,
        backoff_factor: Optional[float] = None,
        max_delay: Optional[float] = None,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
        non_retryable_exceptions: Optional[List[Type[Exception]]] = None
    ):
        """
        Initialize retry handler.
        
        Args:
            max_attempts: Maximum number of attempts (defaults to config)
            base_delay: Base delay in seconds
            backoff_factor: Exponential backoff factor (defaults to config)
            max_delay: Maximum delay in seconds (defaults to config)
            retryable_exceptions: List of exception types that should trigger retry
            non_retryable_exceptions: List of exception types that should not trigger retry
        """
        settings = get_settings()
        
        self.max_attempts = max_attempts or settings.max_retry_attempts
        self.base_delay = base_delay
        self.backoff_factor = backoff_factor or settings.retry_backoff_factor
        self.max_delay = max_delay or settings.retry_max_delay
        
        self.retryable_exceptions = retryable_exceptions or [
            RetryableError,
            ConnectionError,
            TimeoutError,
            OSError,
        ]
        
        self.non_retryable_exceptions = non_retryable_exceptions or [
            NonRetryableError,
            ValueError,
            TypeError,
            KeyError,
            AttributeError,
        ]
    
    def is_retryable(self, exception: Exception) -> bool:
        """
        Check if an exception should trigger a retry.
        
        Args:
            exception: Exception to check
            
        Returns:
            bool: True if the exception is retryable
        """
        # Check non-retryable exceptions first
        for exc_type in self.non_retryable_exceptions:
            if isinstance(exception, exc_type):
                return False
        
        # Check retryable exceptions
        for exc_type in self.retryable_exceptions:
            if isinstance(exception, exc_type):
                return True
        
        # Default to non-retryable for unknown exceptions
        return False
    
    async def execute_async(
        self,
        func: Callable[..., Any],
        *args: Any,
        **kwargs: Any
    ) -> Any:
        """
        Execute an async function with retry logic.
        
        Args:
            func: Async function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Any: Function result
            
        Raises:
            Exception: Last exception if all retries failed
        """
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                logger.debug(
                    "Executing function with retry",
                    function=func.__name__,
                    attempt=attempt + 1,
                    max_attempts=self.max_attempts
                )
                
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(
                        "Function succeeded after retry",
                        function=func.__name__,
                        attempt=attempt + 1
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                logger.warning(
                    "Function failed",
                    function=func.__name__,
                    attempt=attempt + 1,
                    error=str(e),
                    error_type=type(e).__name__
                )
                
                # Check if we should retry
                if not self.is_retryable(e):
                    logger.error(
                        "Non-retryable error, giving up",
                        function=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__
                    )
                    raise e
                
                # Check if we have more attempts
                if attempt + 1 >= self.max_attempts:
                    logger.error(
                        "Max retry attempts reached, giving up",
                        function=func.__name__,
                        attempts=self.max_attempts,
                        error=str(e)
                    )
                    break
                
                # Calculate delay and wait
                delay = calculate_backoff_delay(
                    attempt,
                    self.base_delay,
                    self.backoff_factor,
                    self.max_delay
                )
                
                logger.info(
                    "Retrying after delay",
                    function=func.__name__,
                    delay_seconds=delay,
                    next_attempt=attempt + 2
                )
                
                await asyncio.sleep(delay)
        
        # All retries failed
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("Retry handler failed without exception")
    
    def execute_sync(
        self,
        func: Callable[..., Any],
        *args: Any,
        **kwargs: Any
    ) -> Any:
        """
        Execute a sync function with retry logic.
        
        Args:
            func: Sync function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Any: Function result
            
        Raises:
            Exception: Last exception if all retries failed
        """
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                logger.debug(
                    "Executing function with retry",
                    function=func.__name__,
                    attempt=attempt + 1,
                    max_attempts=self.max_attempts
                )
                
                result = func(*args, **kwargs)
                
                if attempt > 0:
                    logger.info(
                        "Function succeeded after retry",
                        function=func.__name__,
                        attempt=attempt + 1
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                logger.warning(
                    "Function failed",
                    function=func.__name__,
                    attempt=attempt + 1,
                    error=str(e),
                    error_type=type(e).__name__
                )
                
                # Check if we should retry
                if not self.is_retryable(e):
                    logger.error(
                        "Non-retryable error, giving up",
                        function=func.__name__,
                        error=str(e),
                        error_type=type(e).__name__
                    )
                    raise e
                
                # Check if we have more attempts
                if attempt + 1 >= self.max_attempts:
                    logger.error(
                        "Max retry attempts reached, giving up",
                        function=func.__name__,
                        attempts=self.max_attempts,
                        error=str(e)
                    )
                    break
                
                # Calculate delay and wait
                delay = calculate_backoff_delay(
                    attempt,
                    self.base_delay,
                    self.backoff_factor,
                    self.max_delay
                )
                
                logger.info(
                    "Retrying after delay",
                    function=func.__name__,
                    delay_seconds=delay,
                    next_attempt=attempt + 2
                )
                
                time.sleep(delay)
        
        # All retries failed
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("Retry handler failed without exception")


def retry_async(
    max_attempts: Optional[int] = None,
    base_delay: float = 1.0,
    backoff_factor: Optional[float] = None,
    max_delay: Optional[float] = None,
    retryable_exceptions: Optional[List[Type[Exception]]] = None,
    non_retryable_exceptions: Optional[List[Type[Exception]]] = None
):
    """
    Decorator for adding retry logic to async functions.
    
    Args:
        max_attempts: Maximum number of attempts
        base_delay: Base delay in seconds
        backoff_factor: Exponential backoff factor
        max_delay: Maximum delay in seconds
        retryable_exceptions: List of exception types that should trigger retry
        non_retryable_exceptions: List of exception types that should not trigger retry
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            retry_handler = RetryHandler(
                max_attempts=max_attempts,
                base_delay=base_delay,
                backoff_factor=backoff_factor,
                max_delay=max_delay,
                retryable_exceptions=retryable_exceptions,
                non_retryable_exceptions=non_retryable_exceptions
            )
            return await retry_handler.execute_async(func, *args, **kwargs)
        return wrapper
    return decorator


def retry_sync(
    max_attempts: Optional[int] = None,
    base_delay: float = 1.0,
    backoff_factor: Optional[float] = None,
    max_delay: Optional[float] = None,
    retryable_exceptions: Optional[List[Type[Exception]]] = None,
    non_retryable_exceptions: Optional[List[Type[Exception]]] = None
):
    """
    Decorator for adding retry logic to sync functions.
    
    Args:
        max_attempts: Maximum number of attempts
        base_delay: Base delay in seconds
        backoff_factor: Exponential backoff factor
        max_delay: Maximum delay in seconds
        retryable_exceptions: List of exception types that should trigger retry
        non_retryable_exceptions: List of exception types that should not trigger retry
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            retry_handler = RetryHandler(
                max_attempts=max_attempts,
                base_delay=base_delay,
                backoff_factor=backoff_factor,
                max_delay=max_delay,
                retryable_exceptions=retryable_exceptions,
                non_retryable_exceptions=non_retryable_exceptions
            )
            return retry_handler.execute_sync(func, *args, **kwargs)
        return wrapper
    return decorator
