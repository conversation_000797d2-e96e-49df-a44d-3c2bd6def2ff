"""
Configuration management for the Trigger Service.

This module provides centralized configuration management using Pydantic
BaseSettings for environment variable validation and type conversion.
"""

import os
from functools import lru_cache
from typing import Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.
    
    This class uses Pydantic BaseSettings to automatically load and validate
    configuration from environment variables with proper type conversion.
    """
    
    # Application Configuration
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind the server")
    port: int = Field(default=8000, description="Port to bind the server")
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Logging format (json or text)")
    
    # Database Configuration
    database_url: str = Field(..., description="PostgreSQL database URL")
    
    # Auth Service Configuration
    auth_service_url: str = Field(..., description="URL of the authentication service")
    auth_service_api_key: str = Field(..., description="API key for auth service")
    auth_service_timeout: int = Field(default=10, description="Auth service timeout in seconds")
    
    # Workflow Service Configuration
    workflow_service_url: str = Field(..., description="URL of the workflow service")
    workflow_service_api_key: str = Field(..., description="API key for workflow service")
    workflow_service_timeout: int = Field(default=60, description="Workflow service timeout in seconds")
    
    # Google Calendar Configuration
    google_calendar_webhook_url: str = Field(
        ..., 
        description="Public URL for Google Calendar webhooks"
    )
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis connection URL")
    
    # Celery Configuration
    celery_broker_url: Optional[str] = Field(None, description="Celery broker URL")
    celery_result_backend: Optional[str] = Field(None, description="Celery result backend URL")
    
    # Security
    secret_key: str = Field(..., description="Secret key for cryptographic operations")
    api_key: str = Field(..., description="API key for internal service authentication")
    
    # HTTP Configuration
    http_timeout: int = Field(default=30, description="Default HTTP timeout in seconds")
    
    # Retry Configuration
    max_retry_attempts: int = Field(default=5, description="Maximum number of retry attempts")
    retry_backoff_factor: float = Field(default=2.0, description="Exponential backoff factor")
    retry_max_delay: int = Field(default=300, description="Maximum retry delay in seconds")
    
    # Rate Limiting
    rate_limit_requests_per_minute: int = Field(
        default=100, 
        description="Rate limit requests per minute"
    )
    rate_limit_burst: int = Field(default=20, description="Rate limit burst capacity")
    
    # Monitoring
    enable_metrics: bool = Field(default=True, description="Enable Prometheus metrics")
    metrics_port: int = Field(default=9090, description="Port for metrics endpoint")
    
    @validator("log_level")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()
    
    @validator("log_format")
    def validate_log_format(cls, v: str) -> str:
        """Validate log format is one of the allowed values."""
        allowed_formats = ["json", "text"]
        if v.lower() not in allowed_formats:
            raise ValueError(f"Log format must be one of: {allowed_formats}")
        return v.lower()
    
    @validator("database_url")
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v.startswith(("postgresql://", "postgresql+psycopg2://")):
            raise ValueError("Database URL must be a PostgreSQL connection string")
        return v
    
    @validator("redis_url")
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        if not v.startswith("redis://"):
            raise ValueError("Redis URL must start with 'redis://'")
        return v
    
    @validator("port", "metrics_port")
    def validate_port(cls, v: int) -> int:
        """Validate port numbers are in valid range."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v
    
    @validator("max_retry_attempts")
    def validate_max_retry_attempts(cls, v: int) -> int:
        """Validate max retry attempts is positive."""
        if v < 1:
            raise ValueError("Max retry attempts must be at least 1")
        return v
    
    @validator("retry_backoff_factor")
    def validate_retry_backoff_factor(cls, v: float) -> float:
        """Validate retry backoff factor is positive."""
        if v <= 0:
            raise ValueError("Retry backoff factor must be positive")
        return v
    
    def get_celery_broker_url(self) -> str:
        """Get Celery broker URL, defaulting to Redis URL if not set."""
        return self.celery_broker_url or self.redis_url
    
    def get_celery_result_backend(self) -> str:
        """Get Celery result backend URL, defaulting to Redis URL if not set."""
        return self.celery_result_backend or self.redis_url
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
        # Field aliases for environment variables
        fields = {
            "database_url": {"env": "DATABASE_URL"},
            "auth_service_url": {"env": "AUTH_SERVICE_URL"},
            "auth_service_api_key": {"env": "AUTH_SERVICE_API_KEY"},
            "auth_service_timeout": {"env": "AUTH_SERVICE_TIMEOUT"},
            "workflow_service_url": {"env": "WORKFLOW_SERVICE_URL"},
            "workflow_service_api_key": {"env": "WORKFLOW_SERVICE_API_KEY"},
            "workflow_service_timeout": {"env": "WORKFLOW_SERVICE_TIMEOUT"},
            "google_calendar_webhook_url": {"env": "GOOGLE_CALENDAR_WEBHOOK_URL"},
            "redis_url": {"env": "REDIS_URL"},
            "celery_broker_url": {"env": "CELERY_BROKER_URL"},
            "celery_result_backend": {"env": "CELERY_RESULT_BACKEND"},
            "secret_key": {"env": "SECRET_KEY"},
            "api_key": {"env": "API_KEY"},
            "http_timeout": {"env": "HTTP_TIMEOUT"},
            "max_retry_attempts": {"env": "MAX_RETRY_ATTEMPTS"},
            "retry_backoff_factor": {"env": "RETRY_BACKOFF_FACTOR"},
            "retry_max_delay": {"env": "RETRY_MAX_DELAY"},
            "rate_limit_requests_per_minute": {"env": "RATE_LIMIT_REQUESTS_PER_MINUTE"},
            "rate_limit_burst": {"env": "RATE_LIMIT_BURST"},
            "enable_metrics": {"env": "ENABLE_METRICS"},
            "metrics_port": {"env": "METRICS_PORT"},
        }


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    
    This function uses LRU cache to ensure settings are loaded only once
    and reused throughout the application lifecycle.
    
    Returns:
        Settings: Application settings instance
    """
    return Settings()


def get_database_url() -> str:
    """
    Get database URL for SQLAlchemy.
    
    Returns:
        str: Database connection URL
    """
    return get_settings().database_url


def is_development() -> bool:
    """
    Check if the application is running in development mode.
    
    Returns:
        bool: True if in development mode
    """
    return get_settings().debug


def is_production() -> bool:
    """
    Check if the application is running in production mode.
    
    Returns:
        bool: True if in production mode
    """
    return not get_settings().debug
