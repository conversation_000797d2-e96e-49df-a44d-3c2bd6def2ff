"""Initial schema with triggers and trigger_executions tables

Revision ID: 0001
Revises: 
Create Date: 2024-01-01 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create triggers table
    op.create_table(
        'triggers',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=sa.text('gen_random_uuid()')),
        sa.Column('user_id', sa.String(length=255), nullable=False),
        sa.Column('workflow_id', sa.String(length=255), nullable=False),
        sa.Column('trigger_type', sa.String(length=50), nullable=False),
        sa.Column('trigger_name', sa.String(length=255), nullable=False),
        sa.Column('trigger_config', sa.JSON(), nullable=False),
        sa.Column('event_types', postgresql.ARRAY(sa.String()), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('last_triggered_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'workflow_id', 'trigger_type', name='uq_user_workflow_trigger_type')
    )
    
    # Create indexes for triggers table
    op.create_index('idx_triggers_user_id', 'triggers', ['user_id'])
    op.create_index('idx_triggers_workflow_id', 'triggers', ['workflow_id'])
    op.create_index('idx_triggers_type_active', 'triggers', ['trigger_type', 'is_active'])
    op.create_index('idx_triggers_created_at', 'triggers', ['created_at'])
    
    # Create trigger_executions table
    op.create_table(
        'trigger_executions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=sa.text('gen_random_uuid()')),
        sa.Column('trigger_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('event_data', sa.JSON(), nullable=False),
        sa.Column('workflow_execution_id', sa.String(length=255), nullable=True),
        sa.Column('status', sa.String(length=50), nullable=False, default='pending'),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=False, default=0),
        sa.Column('executed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['trigger_id'], ['triggers.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for trigger_executions table
    op.create_index('idx_trigger_executions_trigger_id', 'trigger_executions', ['trigger_id'])
    op.create_index('idx_trigger_executions_status', 'trigger_executions', ['status'])
    op.create_index('idx_trigger_executions_executed_at', 'trigger_executions', ['executed_at'])
    op.create_index('idx_trigger_executions_workflow_id', 'trigger_executions', ['workflow_execution_id'])
    
    # Create function to update updated_at timestamp
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    # Create trigger to automatically update updated_at
    op.execute("""
        CREATE TRIGGER update_triggers_updated_at
            BEFORE UPDATE ON triggers
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop triggers
    op.execute("DROP TRIGGER IF EXISTS update_triggers_updated_at ON triggers;")
    op.execute("DROP FUNCTION IF EXISTS update_updated_at_column();")
    
    # Drop indexes for trigger_executions
    op.drop_index('idx_trigger_executions_workflow_id', table_name='trigger_executions')
    op.drop_index('idx_trigger_executions_executed_at', table_name='trigger_executions')
    op.drop_index('idx_trigger_executions_status', table_name='trigger_executions')
    op.drop_index('idx_trigger_executions_trigger_id', table_name='trigger_executions')
    
    # Drop trigger_executions table
    op.drop_table('trigger_executions')
    
    # Drop indexes for triggers
    op.drop_index('idx_triggers_created_at', table_name='triggers')
    op.drop_index('idx_triggers_type_active', table_name='triggers')
    op.drop_index('idx_triggers_workflow_id', table_name='triggers')
    op.drop_index('idx_triggers_user_id', table_name='triggers')
    
    # Drop triggers table
    op.drop_table('triggers')
