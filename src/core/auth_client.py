"""
Auth Client - Interface to the authentication service.

This module provides the AuthClient class for retrieving credentials
and managing authentication with external services.
"""

from typing import Dict, Any, Optional
import httpx
import structlog

from src.utils.config import get_settings

logger = structlog.get_logger(__name__)


class AuthClient:
    """
    Client for interacting with the authentication service.
    
    This class handles credential retrieval, token refresh, and other
    authentication-related operations with the external auth service.
    """
    
    def __init__(self):
        """Initialize the auth client."""
        self.settings = get_settings()
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.settings.auth_service_api_key}"
            }
        )
    
    async def get_credentials(
        self,
        user_id: str,
        service_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve credentials for a user and service.
        
        Args:
            user_id: ID of the user
            service_name: Name of the service (e.g., 'google_calendar')
            
        Returns:
            Dict[str, Any]: Credentials data, or None if not found
        """
        try:
            logger.info(
                "Retrieving credentials",
                user_id=user_id,
                service_name=service_name
            )
            
            response = await self.client.get(
                f"{self.settings.auth_service_url}/api/v1/credentials/{user_id}/{service_name}"
            )
            
            if response.status_code == 200:
                credentials = response.json()
                logger.info(
                    "Credentials retrieved successfully",
                    user_id=user_id,
                    service_name=service_name
                )
                return credentials
            elif response.status_code == 404:
                logger.warning(
                    "Credentials not found",
                    user_id=user_id,
                    service_name=service_name
                )
                return None
            else:
                logger.error(
                    "Failed to retrieve credentials",
                    status_code=response.status_code,
                    response=response.text,
                    user_id=user_id,
                    service_name=service_name
                )
                return None
                
        except Exception as e:
            logger.error(
                "Error retrieving credentials",
                error=str(e),
                user_id=user_id,
                service_name=service_name
            )
            return None
    
    async def refresh_token(
        self,
        user_id: str,
        service_name: str,
        refresh_token: str
    ) -> Optional[Dict[str, Any]]:
        """
        Refresh an access token using a refresh token.
        
        Args:
            user_id: ID of the user
            service_name: Name of the service
            refresh_token: Refresh token to use
            
        Returns:
            Dict[str, Any]: New credentials data, or None if failed
        """
        try:
            logger.info(
                "Refreshing token",
                user_id=user_id,
                service_name=service_name
            )
            
            request_data = {
                "refresh_token": refresh_token
            }
            
            response = await self.client.post(
                f"{self.settings.auth_service_url}/api/v1/credentials/{user_id}/{service_name}/refresh",
                json=request_data
            )
            
            if response.status_code == 200:
                credentials = response.json()
                logger.info(
                    "Token refreshed successfully",
                    user_id=user_id,
                    service_name=service_name
                )
                return credentials
            else:
                logger.error(
                    "Failed to refresh token",
                    status_code=response.status_code,
                    response=response.text,
                    user_id=user_id,
                    service_name=service_name
                )
                return None
                
        except Exception as e:
            logger.error(
                "Error refreshing token",
                error=str(e),
                user_id=user_id,
                service_name=service_name
            )
            return None
    
    async def validate_credentials(
        self,
        user_id: str,
        service_name: str
    ) -> bool:
        """
        Validate that credentials exist and are valid for a user and service.
        
        Args:
            user_id: ID of the user
            service_name: Name of the service
            
        Returns:
            bool: True if credentials are valid, False otherwise
        """
        try:
            response = await self.client.get(
                f"{self.settings.auth_service_url}/api/v1/credentials/{user_id}/{service_name}/validate"
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(
                "Error validating credentials",
                error=str(e),
                user_id=user_id,
                service_name=service_name
            )
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
