"""
Trigger Manager - Core orchestration for trigger lifecycle management.

This module provides the main TriggerManager class that coordinates trigger
detection, adapter management, and workflow execution.
"""

from typing import Dict, List, Optional, Any
from uuid import UUID
import asyncio

from src.adapters.base import BaseTriggerAdapter, TriggerEvent

class TriggerManager:
    """
    Central manager for trigger lifecycle and event processing.
    
    The TriggerManager coordinates between different trigger adapters,
    manages trigger configurations, and orchestrates workflow execution
    when triggers are activated.
    """
    
    def __init__(self):
        """Initialize the trigger manager."""
        self._adapters: Dict[str, BaseTriggerAdapter] = {}
        self._active_triggers: Dict[UUID, Dict[str, Any]] = {}
    
    def register_adapter(self, adapter: BaseTriggerAdapter) -> None:
        """
        Register a trigger adapter with the manager.
        
        Args:
            adapter: The adapter instance to register
        """
        self._adapters[adapter.adapter_name] = adapter
    
    def get_adapter(self, adapter_name: str) -> Optional[BaseTriggerAdapter]:
        """
        Get a registered adapter by name.
        
        Args:
            adapter_name: Name of the adapter to retrieve
            
        Returns:
            BaseTriggerAdapter: The adapter instance, or None if not found
        """
        return self._adapters.get(adapter_name)
    
    async def create_trigger(
        self,
        trigger_id: UUID,
        adapter_name: str,
        trigger_config: Dict[str, Any],
        event_types: List[str]
    ) -> bool:
        """
        Create a new trigger with the specified adapter.
        
        Args:
            trigger_id: Unique identifier for the trigger
            adapter_name: Name of the adapter to use
            trigger_config: Adapter-specific configuration
            event_types: List of event types to monitor
            
        Returns:
            bool: True if trigger was created successfully
        """
        adapter = self.get_adapter(adapter_name)
        if not adapter:
            return False
        
        # Validate configuration
        if not await adapter.validate_config(trigger_config):
            return False
        
        # Setup trigger with adapter
        success = await adapter.setup_trigger(trigger_id, trigger_config, event_types)
        if success:
            self._active_triggers[trigger_id] = {
                "adapter_name": adapter_name,
                "config": trigger_config,
                "event_types": event_types
            }
        
        return success
    
    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove an existing trigger.
        
        Args:
            trigger_id: Unique identifier for the trigger to remove
            
        Returns:
            bool: True if trigger was removed successfully
        """
        if trigger_id not in self._active_triggers:
            return False
        
        trigger_info = self._active_triggers[trigger_id]
        adapter = self.get_adapter(trigger_info["adapter_name"])
        
        if adapter:
            success = await adapter.remove_trigger(trigger_id)
            if success:
                del self._active_triggers[trigger_id]
            return success
        
        return False
    
    async def process_event(
        self,
        adapter_name: str,
        raw_event: Dict[str, Any]
    ) -> Optional[TriggerEvent]:
        """
        Process an incoming event from an adapter.
        
        Args:
            adapter_name: Name of the adapter that received the event
            raw_event: Raw event data from the external service
            
        Returns:
            TriggerEvent: Processed event data, or None if event should be ignored
        """
        adapter = self.get_adapter(adapter_name)
        if not adapter:
            return None
        
        return await adapter.process_event(raw_event)
    
    async def health_check(self) -> Dict[str, bool]:
        """
        Check health of all registered adapters.
        
        Returns:
            Dict[str, bool]: Health status for each adapter
        """
        health_status = {}
        for name, adapter in self._adapters.items():
            try:
                health_status[name] = await adapter.health_check()
            except Exception:
                health_status[name] = False
        
        return health_status
