"""
Trigger Manager - Core orchestration for trigger lifecycle management.

This module provides the main TriggerManager class that coordinates trigger
detection, adapter management, and workflow execution.
"""

from typing import Dict, List, Optional, Any
from uuid import UUID
import asyncio
from datetime import datetime

from sqlalchemy import select, and_, desc

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerConfiguration,
    TriggerEventType,
    AdapterHealthStatus,
)
from src.database.models import Trigger, TriggerExecution
from src.database.connection import get_async_session
from src.core.workflow_executor import WorkflowExecutor
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class TriggerManager:
    """
    Central manager for trigger lifecycle and event processing.

    The TriggerManager coordinates between different trigger adapters,
    manages trigger configurations, and orchestrates workflow execution
    when triggers are activated.
    """

    def __init__(self):
        """Initialize the trigger manager."""
        self._adapters: Dict[str, BaseTriggerAdapter] = {}
        self._workflow_executor = WorkflowExecutor()
        self._retry_handler = RetryHandler()
        logger.info("TriggerManager initialized")

    def register_adapter(self, adapter: BaseTriggerAdapter) -> None:
        """
        Register a trigger adapter with the manager.

        Args:
            adapter: The adapter instance to register
        """
        self._adapters[adapter.adapter_name] = adapter
        logger.info(f"Registered adapter: {adapter.adapter_name}")

    def get_adapter(self, adapter_name: str) -> Optional[BaseTriggerAdapter]:
        """
        Get a registered adapter by name.

        Args:
            adapter_name: Name of the adapter to retrieve

        Returns:
            BaseTriggerAdapter: The adapter instance, or None if not found
        """
        return self._adapters.get(adapter_name)

    def list_adapters(self) -> List[str]:
        """
        Get list of registered adapter names.

        Returns:
            List[str]: List of adapter names
        """
        return list(self._adapters.keys())

    async def create_trigger(
        self,
        user_id: str,
        workflow_id: str,
        trigger_type: str,
        trigger_name: str,
        trigger_config: Dict[str, Any],
        event_types: List[TriggerEventType],
    ) -> Optional[UUID]:
        """
        Create a new trigger with the specified adapter.

        Args:
            user_id: ID of the user creating the trigger
            workflow_id: ID of the workflow to execute
            trigger_type: Type of trigger (adapter name)
            trigger_name: Human-readable name for the trigger
            trigger_config: Adapter-specific configuration
            event_types: List of event types to monitor

        Returns:
            UUID: Trigger ID if created successfully, None otherwise
        """
        try:
            adapter = self.get_adapter(trigger_type)
            if not adapter:
                logger.error(f"Adapter not found: {trigger_type}")
                return None

            # Create trigger in database first
            async with get_async_session() as session:
                trigger = Trigger(
                    user_id=user_id,
                    workflow_id=workflow_id,
                    trigger_type=trigger_type,
                    trigger_name=trigger_name,
                    trigger_config=trigger_config,
                    event_types=[et.value for et in event_types],
                    is_active=True,
                )
                session.add(trigger)
                await session.flush()  # Get the ID

                # Create trigger configuration for adapter
                trigger_config_obj = TriggerConfiguration(
                    trigger_id=trigger.id,
                    user_id=user_id,
                    workflow_id=workflow_id,
                    event_types=event_types,
                    config=trigger_config,
                    is_active=True,
                )

                # Register with adapter
                success = await adapter.register_trigger(trigger_config_obj)
                if success:
                    await session.commit()
                    logger.info(f"Successfully created trigger {trigger.id}")
                    return trigger.id
                else:
                    await session.rollback()
                    logger.error(
                        f"Failed to register trigger with adapter {trigger_type}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Failed to create trigger", error=str(e))
            return None

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove an existing trigger.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if trigger was removed successfully
        """
        try:
            async with get_async_session() as session:
                # Get trigger from database
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found in database")
                    return False

                # Get adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if not adapter:
                    logger.error(f"Adapter not found: {trigger.trigger_type}")
                    return False

                # Unregister from adapter
                success = await adapter.unregister_trigger(trigger_id)
                if success:
                    # Remove from database
                    await session.delete(trigger)
                    await session.commit()
                    logger.info(f"Successfully removed trigger {trigger_id}")
                    return True
                else:
                    logger.error(
                        f"Failed to unregister trigger {trigger_id} from adapter"
                    )
                    return False

        except Exception as e:
            logger.error(f"Failed to remove trigger {trigger_id}", error=str(e))
            return False

    async def process_event(self, adapter_name: str, raw_event: Dict[str, Any]) -> bool:
        """
        Process an incoming event from an adapter and execute matching workflows.

        Args:
            adapter_name: Name of the adapter that received the event
            raw_event: Raw event data from the external service

        Returns:
            bool: True if event was processed successfully
        """
        try:
            adapter = self.get_adapter(adapter_name)
            if not adapter:
                logger.error(f"Adapter not found: {adapter_name}")
                return False

            # Process the raw event
            trigger_event = await adapter.process_event(raw_event)
            if not trigger_event:
                logger.debug(f"Event ignored by adapter {adapter_name}")
                return True  # Not an error, just ignored

            # Find matching triggers
            matching_triggers = await self._find_matching_triggers(
                adapter_name, trigger_event
            )

            # Execute workflows for matching triggers
            for trigger in matching_triggers:
                await self._execute_trigger_workflow(trigger, trigger_event)

            logger.info(
                f"Processed event {trigger_event.event_id} from {adapter_name}, "
                f"matched {len(matching_triggers)} triggers"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to process event from {adapter_name}", error=str(e))
            return False

    async def health_check(self) -> Dict[str, AdapterHealthStatus]:
        """
        Check health of all registered adapters.

        Returns:
            Dict[str, AdapterHealthStatus]: Health status for each adapter
        """
        health_status = {}
        for name, adapter in self._adapters.items():
            try:
                health_status[name] = await adapter.health_check()
            except Exception as e:
                health_status[name] = AdapterHealthStatus(
                    is_healthy=False, last_check=datetime.now(), error_message=str(e)
                )

        return health_status

    async def get_triggers_for_user(self, user_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific user.

        Args:
            user_id: ID of the user

        Returns:
            List[Trigger]: List of user's triggers
        """
        try:
            async with get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.user_id == user_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Failed to get triggers for user {user_id}", error=str(e))
            return []

    async def get_triggers_for_workflow(self, workflow_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            List[Trigger]: List of workflow's triggers
        """
        try:
            async with get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.workflow_id == workflow_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(
                f"Failed to get triggers for workflow {workflow_id}", error=str(e)
            )
            return []

    async def toggle_trigger(self, trigger_id: UUID, is_active: bool) -> bool:
        """
        Enable or disable a trigger.

        Args:
            trigger_id: ID of the trigger
            is_active: Whether to activate or deactivate

        Returns:
            bool: True if toggle was successful
        """
        try:
            async with get_async_session() as session:
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found")
                    return False

                trigger.is_active = is_active
                await session.commit()

                # Update adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if adapter:
                    if is_active:
                        await adapter.resume_trigger(trigger_id)
                    else:
                        await adapter.pause_trigger(trigger_id)

                logger.info(
                    f"Trigger {trigger_id} {'activated' if is_active else 'deactivated'}"
                )
                return True

        except Exception as e:
            logger.error(f"Failed to toggle trigger {trigger_id}", error=str(e))
            return False

    async def _find_matching_triggers(
        self, adapter_name: str, trigger_event: TriggerEvent
    ) -> List[Trigger]:
        """
        Find triggers that match the given event.

        Args:
            adapter_name: Name of the adapter
            trigger_event: Event to match against

        Returns:
            List[Trigger]: List of matching triggers
        """
        try:
            async with get_async_session() as session:
                # Find active triggers for this adapter type that monitor this event type
                result = await session.execute(
                    select(Trigger).where(
                        and_(
                            Trigger.trigger_type == adapter_name,
                            Trigger.is_active == True,
                            Trigger.event_types.contains(
                                [trigger_event.event_type.value]
                            ),
                        )
                    )
                )
                triggers = result.scalars().all()

                # Additional filtering based on trigger configuration could be added here
                # For now, return all matching triggers
                return triggers

        except Exception as e:
            logger.error(f"Failed to find matching triggers", error=str(e))
            return []

    async def _execute_trigger_workflow(
        self, trigger: Trigger, trigger_event: TriggerEvent
    ) -> None:
        """
        Execute workflow for a triggered event.

        Args:
            trigger: Trigger that was activated
            trigger_event: Event that activated the trigger
        """
        try:
            # Create execution record
            async with get_async_session() as session:
                execution = TriggerExecution(
                    trigger_id=trigger.id,
                    event_data=trigger_event.dict(),
                    status="pending",
                )
                session.add(execution)
                await session.flush()  # Get the ID

                try:
                    # Get workflow data (this would typically come from a workflow service)
                    workflow_data = {"workflow_id": trigger.workflow_id}  # Placeholder

                    # Execute workflow using retry handler
                    correlation_id = await self._retry_handler.execute_async(
                        self._workflow_executor.execute_workflow,
                        trigger.user_id,
                        trigger.workflow_id,
                        workflow_data,
                        trigger_event.dict(),
                    )

                    if correlation_id:
                        execution.workflow_execution_id = correlation_id
                        execution.status = "success"
                        execution.completed_at = datetime.now()

                        # Update trigger last_triggered_at
                        trigger.last_triggered_at = datetime.now()

                        logger.info(
                            f"Successfully executed workflow for trigger {trigger.id}, "
                            f"correlation_id: {correlation_id}"
                        )
                    else:
                        execution.status = "failed"
                        execution.error_message = (
                            "Workflow execution returned no correlation ID"
                        )
                        execution.completed_at = datetime.now()

                except Exception as e:
                    execution.status = "failed"
                    execution.error_message = str(e)
                    execution.completed_at = datetime.now()
                    logger.error(
                        f"Failed to execute workflow for trigger {trigger.id}",
                        error=str(e),
                    )

                await session.commit()

        except Exception as e:
            logger.error(
                f"Failed to process trigger execution for trigger {trigger.id}",
                error=str(e),
            )

    async def get_execution_history(
        self, trigger_id: UUID, limit: int = 100
    ) -> List[TriggerExecution]:
        """
        Get execution history for a trigger.

        Args:
            trigger_id: ID of the trigger
            limit: Maximum number of executions to return

        Returns:
            List[TriggerExecution]: List of executions
        """
        try:
            async with get_async_session() as session:
                result = await session.execute(
                    select(TriggerExecution)
                    .where(TriggerExecution.trigger_id == trigger_id)
                    .order_by(desc(TriggerExecution.executed_at))
                    .limit(limit)
                )
                return result.scalars().all()

        except Exception as e:
            logger.error(
                f"Failed to get execution history for trigger {trigger_id}",
                error=str(e),
            )
            return []

    async def get_adapter_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all adapters.

        Returns:
            Dict[str, Dict[str, Any]]: Statistics for each adapter
        """
        stats = {}
        for name, adapter in self._adapters.items():
            try:
                health = await adapter.health_check()
                stats[name] = {
                    "is_healthy": health.is_healthy,
                    "last_check": health.last_check.isoformat(),
                    "active_triggers": adapter.get_trigger_count(),
                    "error_message": health.error_message,
                }
            except Exception as e:
                stats[name] = {
                    "is_healthy": False,
                    "last_check": datetime.now().isoformat(),
                    "active_triggers": 0,
                    "error_message": str(e),
                }

        return stats
