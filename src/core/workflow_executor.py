"""
Workflow Executor - Handles execution of workflows when triggers are activated.

This module provides the WorkflowExecutor class that formats and sends
workflow execution requests to the workflow service.
"""

from typing import Dict, Any, Optional
import httpx
import structlog

from src.utils.config import get_settings

logger = structlog.get_logger(__name__)


class WorkflowExecutor:
    """
    Handles workflow execution requests to the workflow service.
    
    This class is responsible for formatting trigger event data into the
    proper workflow execution format and making HTTP requests to the
    workflow service API.
    """
    
    def __init__(self):
        """Initialize the workflow executor."""
        self.settings = get_settings()
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            headers={"Content-Type": "application/json"}
        )
    
    async def execute_workflow(
        self,
        user_id: str,
        workflow_id: str,
        workflow_data: Dict[str, Any],
        event_data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Execute a workflow with the provided event data.
        
        Args:
            user_id: ID of the user who owns the workflow
            workflow_id: ID of the workflow to execute
            workflow_data: Complete workflow configuration from database
            event_data: Event data from the trigger
            
        Returns:
            str: Correlation ID from the workflow service, or None if failed
        """
        try:
            # Transform event data into workflow payload format
            payload = self._transform_event_to_payload(event_data, workflow_data)
            
            # Prepare the workflow execution request
            request_data = {
                "approval": True,
                "payload": payload,
                "user_id": user_id,
                "workflow_id": workflow_id
            }
            
            logger.info(
                "Executing workflow",
                user_id=user_id,
                workflow_id=workflow_id,
                event_type=event_data.get("event_type")
            )
            
            # Make the HTTP request to workflow service
            response = await self.client.post(
                f"{self.settings.workflow_service_url}/api/v1/workflow-execute/execute",
                json=request_data,
                headers={"Authorization": f"Bearer {self.settings.workflow_service_api_key}"}
            )
            
            if response.status_code == 200:
                result = response.json()
                correlation_id = result.get("correlationId")
                
                logger.info(
                    "Workflow execution initiated",
                    correlation_id=correlation_id,
                    user_id=user_id,
                    workflow_id=workflow_id
                )
                
                return correlation_id
            else:
                logger.error(
                    "Workflow execution failed",
                    status_code=response.status_code,
                    response=response.text,
                    user_id=user_id,
                    workflow_id=workflow_id
                )
                return None
                
        except Exception as e:
            logger.error(
                "Error executing workflow",
                error=str(e),
                user_id=user_id,
                workflow_id=workflow_id
            )
            return None
    
    def _transform_event_to_payload(
        self,
        event_data: Dict[str, Any],
        workflow_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Transform trigger event data into workflow payload format.
        
        Args:
            event_data: Event data from the trigger
            workflow_data: Workflow configuration from database
            
        Returns:
            Dict[str, Any]: Formatted payload for workflow execution
        """
        # Extract relevant fields from event data
        event_fields = self._extract_event_fields(event_data)
        
        # Create user payload template with event data
        user_payload_template = {}
        user_dependent_fields = []
        
        for field_name, field_value in event_fields.items():
            user_dependent_fields.append(field_name)
            user_payload_template[field_name] = {
                "transition_id": "transition-1",  # Default transition
                "value": field_value
            }
        
        return {
            "user_dependent_fields": user_dependent_fields,
            "user_payload_template": user_payload_template
        }
    
    def _extract_event_fields(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract relevant fields from event data for workflow execution.
        
        Args:
            event_data: Event data from the trigger
            
        Returns:
            Dict[str, Any]: Extracted fields for workflow
        """
        extracted = {
            "event_type": event_data.get("event_type"),
            "event_id": event_data.get("event_id"),
            "timestamp": event_data.get("timestamp"),
            "source": event_data.get("source")
        }
        
        # Add adapter-specific data
        if "data" in event_data:
            extracted.update(event_data["data"])
        
        return extracted
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
