# Application Configuration
DEBUG=true
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration
DATABASE_URL=postgresql://trigger_user:trigger_password@localhost:5432/trigger_db

# Auth Service Configuration
AUTH_SERVICE_URL=https://auth-service.example.com
AUTH_SERVICE_API_KEY=your-auth-service-api-key

# Workflow Service Configuration
WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key

# Google Calendar Configuration
GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
API_KEY=your-api-key-for-internal-services

# External Service Timeouts (seconds)
HTTP_TIMEOUT=30
AUTH_SERVICE_TIMEOUT=10
WORKFLOW_SERVICE_TIMEOUT=60

# Retry Configuration
MAX_RETRY_ATTEMPTS=5
RETRY_BACKOFF_FACTOR=2
RETRY_MAX_DELAY=300

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
