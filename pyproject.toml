[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "trigger-service"
version = "0.1.0"
description = "Event-based trigger service for workflow automation"
authors = ["Trigger Service Team <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "0.104.1"
uvicorn = {extras = ["standard"], version = "0.24.0"}
sqlalchemy = "2.0.23"
alembic = "1.12.1"
psycopg2-binary = "2.9.9"
google-api-python-client = "2.108.0"
google-auth = "2.23.4"
google-auth-oauthlib = "1.1.0"
pydantic = "2.5.0"
pydantic-settings = "2.1.0"
celery = "5.3.4"
redis = "5.0.1"
httpx = "0.25.2"
structlog = "23.2.0"
python-multipart = "0.0.6"
python-dateutil = "2.8.2"

[tool.poetry.group.dev.dependencies]
pytest = "7.4.3"
pytest-asyncio = "0.21.1"
pytest-cov = "4.1.0"
pytest-mock = "3.12.0"
black = "23.11.0"
isort = "5.12.0"
flake8 = "6.1.0"
mypy = "1.7.1"
types-redis = "********"
types-python-dateutil = "*********"
pre-commit = "3.5.0"
ipython = "8.17.2"
ipdb = "0.13.13"
locust = "2.17.0"
mkdocs = "1.5.3"
mkdocs-material = "9.4.8"

[tool.poetry.scripts]
trigger-service = "src.main:main"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src", "tests"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "google.*",
    "celery.*",
    "structlog.*",
]
ignore_missing_imports = true

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
