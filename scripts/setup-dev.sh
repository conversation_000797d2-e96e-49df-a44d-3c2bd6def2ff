#!/bin/bash

# Development setup script for Trigger Service
set -e

echo "🔧 Setting up Trigger Service development environment..."

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "   curl -sSL https://install.python-poetry.org | python3 -"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies with Poetry..."
poetry install

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Created .env file. Please update it with your configuration."
else
    echo "✅ .env file already exists."
fi

# Check if Docker is running (for database)
if command -v docker &> /dev/null && docker info &> /dev/null; then
    echo "🐳 Docker is available. You can start the development database with:"
    echo "   docker-compose -f docker-compose.dev.yml up -d postgres redis"
else
    echo "⚠️  Docker is not available. You'll need to set up PostgreSQL and Redis manually."
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Update .env file with your configuration"
echo "2. Start the database: docker-compose -f docker-compose.dev.yml up -d postgres redis"
echo "3. Run migrations: poetry run alembic upgrade head"
echo "4. Start the service: poetry run python scripts/start.py"
echo "   or: poetry run python -m src.main"
echo ""
echo "For development with hot reload:"
echo "   poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000"
