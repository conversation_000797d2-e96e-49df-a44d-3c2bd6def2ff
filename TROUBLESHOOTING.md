# Troubleshooting Guide

This document provides solutions to common issues you might encounter when setting up or running the Trigger Service.

## Installation Issues

### Poetry Installation Error: "No file/folder found for package trigger-service"

**Problem**: Poetry tries to install the current project as a package but can't find it.

**Solution**: The `pyproject.toml` has been configured with `package-mode = false` to resolve this. If you still encounter this issue:

1. Make sure you're using the latest `pyproject.toml` file
2. Run `poetry install --no-root` to skip installing the current project
3. Alternatively, use pip installation method

### Import Errors When Running the Application

**Problem**: Python can't find the `src` modules.

**Solutions**:
1. Make sure you're running from the project root directory
2. Use the provided startup script: `python scripts/start.py`
3. Or run with module syntax: `python -m src.main`
4. Ensure your PYTHONPATH includes the project root

### Database Connection Errors

**Problem**: Can't connect to PostgreSQL database.

**Solutions**:
1. Make sure PostgreSQL is running:
   ```bash
   # Using Docker
   docker-compose -f docker-compose.dev.yml up -d postgres
   
   # Check if container is running
   docker ps
   ```

2. Verify database credentials in `.env` file:
   ```bash
   DATABASE_URL=postgresql://trigger_user:trigger_password@localhost:5432/trigger_db
   ```

3. Test database connection:
   ```bash
   psql postgresql://trigger_user:trigger_password@localhost:5432/trigger_db
   ```

### Redis Connection Errors

**Problem**: Can't connect to Redis.

**Solutions**:
1. Start Redis:
   ```bash
   # Using Docker
   docker-compose -f docker-compose.dev.yml up -d redis
   
   # Or install locally
   brew install redis  # macOS
   sudo apt-get install redis-server  # Ubuntu
   ```

2. Test Redis connection:
   ```bash
   redis-cli ping
   ```

## Runtime Issues

### Configuration Validation Errors

**Problem**: Pydantic validation errors on startup.

**Solutions**:
1. Check your `.env` file has all required variables
2. Copy from template: `cp .env.example .env`
3. Verify environment variable formats (URLs, ports, etc.)

### Migration Errors

**Problem**: Alembic migration failures.

**Solutions**:
1. Make sure database is running and accessible
2. Check if database exists:
   ```bash
   createdb -h localhost -U trigger_user trigger_db
   ```
3. Run migrations step by step:
   ```bash
   poetry run alembic current
   poetry run alembic upgrade head
   ```

### Port Already in Use

**Problem**: Port 8000 is already in use.

**Solutions**:
1. Change port in `.env` file: `PORT=8001`
2. Kill process using the port:
   ```bash
   lsof -ti:8000 | xargs kill -9
   ```
3. Use a different port when starting:
   ```bash
   poetry run uvicorn src.main:app --port 8001
   ```

## Development Issues

### Hot Reload Not Working

**Problem**: Code changes don't trigger automatic restart.

**Solutions**:
1. Use uvicorn with reload flag:
   ```bash
   poetry run uvicorn src.main:app --reload
   ```
2. Make sure you're in development mode (`DEBUG=true` in `.env`)

### Import Errors in IDE

**Problem**: IDE can't resolve imports from `src` package.

**Solutions**:
1. Configure your IDE's Python interpreter to use the Poetry virtual environment
2. Add the project root to your IDE's Python path
3. For VSCode, create `.vscode/settings.json`:
   ```json
   {
     "python.defaultInterpreterPath": ".venv/bin/python",
     "python.analysis.extraPaths": ["./src"]
   }
   ```

### Docker Issues

**Problem**: Docker containers won't start or have permission issues.

**Solutions**:
1. Make sure Docker is running
2. Check Docker permissions:
   ```bash
   sudo usermod -aG docker $USER
   ```
3. Restart Docker service:
   ```bash
   sudo systemctl restart docker
   ```

## Testing Issues

### Tests Can't Find Modules

**Problem**: pytest can't import modules from `src`.

**Solutions**:
1. Run tests with Poetry: `poetry run pytest`
2. Or install package in development mode: `pip install -e .`
3. Add `src` to PYTHONPATH: `PYTHONPATH=src pytest`

### Database Tests Failing

**Problem**: Tests fail due to database issues.

**Solutions**:
1. Use a separate test database
2. Set `DATABASE_URL` for tests to point to test database
3. Make sure test database is clean before each test

## Performance Issues

### Slow Startup

**Problem**: Application takes long time to start.

**Possible Causes & Solutions**:
1. Database connection timeout - check database connectivity
2. Large number of dependencies - consider using Docker for consistent environment
3. Debug mode enabled - set `DEBUG=false` for production

### Memory Usage

**Problem**: High memory consumption.

**Solutions**:
1. Check database connection pool settings
2. Monitor for memory leaks in long-running processes
3. Adjust worker processes in production deployment

## Getting Help

If you encounter issues not covered here:

1. Check the application logs for detailed error messages
2. Enable debug logging: `LOG_LEVEL=DEBUG` in `.env`
3. Review the [README.md](README.md) for setup instructions
4. Check the [Product Specification Document](TRIGGER_SERVICE_PSD.md) for architecture details

## Useful Commands

```bash
# Check application health
curl http://localhost:8000/api/v1/health

# View application logs
poetry run python -m src.main

# Run with debug logging
LOG_LEVEL=DEBUG poetry run python -m src.main

# Check database connection
poetry run python -c "from src.database.connection import db_manager; import asyncio; print(asyncio.run(db_manager.health_check()))"

# Run migrations
poetry run alembic upgrade head

# Check migration status
poetry run alembic current

# Run tests
poetry run pytest -v

# Format code
poetry run black src tests
poetry run isort src tests

# Type checking
poetry run mypy src
```
