version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trigger-service-postgres
    environment:
      POSTGRES_DB: trigger_db
      POSTGRES_USER: trigger_user
      POSTGRES_PASSWORD: trigger_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trigger_user -d trigger_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trigger-service-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-network

  # Trigger Service Application
  trigger-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trigger-service-app
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=********************************************************/trigger_db
      - REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=https://auth-service.example.com
      - AUTH_SERVICE_API_KEY=your-auth-service-api-key
      - WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
      - WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key
      - GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar
      - SECRET_KEY=your-secret-key-here
      - API_KEY=your-api-key-for-internal-services
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - trigger-network
    restart: unless-stopped

  # Celery Worker (for background tasks)
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trigger-service-celery
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=********************************************************/trigger_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    command: ["celery", "-A", "src.core.celery_app", "worker", "--loglevel=info"]
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - trigger-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  trigger-network:
    driver: bridge
