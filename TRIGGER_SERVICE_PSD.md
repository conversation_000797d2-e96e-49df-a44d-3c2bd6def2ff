# Trigger Service - Product Specification Document

## 1. Executive Summary

The Trigger Service is a backend microservice designed for a workflow automation platform that enables users to set up event-based triggers and schedulers. The service follows an adapter/connector pattern for extensibility and focuses on Google Calendar integration as the initial proof of concept.

## 2. System Architecture

### 2.1 High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chat Interface│    │  Global Agent   │    │ Workflow Engine │
│                 │    │                 │    │                 │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ Setup Trigger        │ Setup Workflow       │ Execute
          │ via API              │ for User             │ Workflow
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │     Trigger Service         │
                    │  ┌─────────────────────────┐│
                    │  │   Trigger Manager       ││
                    │  │                         ││
                    │  │ ┌─────────────────────┐ ││
                    │  │ │ Google Calendar     │ ││
                    │  │ │ Adapter             │ ││
                    │  │ └─────────────────────┘ ││
                    │  │                         ││
                    │  │ ┌─────────────────────┐ ││
                    │  │ │ Future Adapters     │ ││
                    │  │ │ (Slack, Email, etc.)│ ││
                    │  │ └─────────────────────┘ ││
                    │  └─────────────────────────┘│
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │     PostgreSQL Database     │
                    │   (Triggers Configuration)  │
                    └─────────────────────────────┘
```

### 2.2 Adapter Pattern Implementation

The service implements the adapter pattern to ensure extensibility:

- **Base Trigger Interface**: Abstract interface defining common trigger operations
- **Google Calendar Adapter**: Concrete implementation for Google Calendar events
- **Trigger Manager**: Orchestrates trigger detection and workflow execution
- **Future Adapters**: Easily pluggable for Slack, Email, GitHub, etc.

## 3. Core Components

### 3.1 Trigger Manager

- Coordinates trigger detection across all adapters
- Manages trigger lifecycle (create, update, delete, pause/resume)
- Handles workflow execution requests
- Implements retry mechanisms and error handling

### 3.2 Google Calendar Adapter

- Monitors Google Calendar events via webhooks and polling
- Handles OAuth2 authentication with Auth service
- Processes event types: creation, updates, deletions, reminders
- Transforms calendar events into standardized trigger data

### 3.3 Database Layer

- Stores trigger configurations and metadata
- Maintains trigger execution history
- Handles database migrations and schema updates

### 3.4 API Layer

- RESTful API for trigger management
- Webhook endpoints for external services
- Health check and monitoring endpoints

## 4. Database Schema

### 4.1 Triggers Table

```sql
CREATE TABLE triggers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    workflow_id VARCHAR(255) NOT NULL,
    trigger_type VARCHAR(50) NOT NULL, -- 'google_calendar', 'slack', etc.
    trigger_name VARCHAR(255) NOT NULL,
    trigger_config JSONB NOT NULL, -- Adapter-specific configuration
    event_types TEXT[] NOT NULL, -- ['created', 'updated', 'deleted', 'reminder']
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_triggered_at TIMESTAMP WITH TIME ZONE,

    CONSTRAINT fk_user_workflow UNIQUE(user_id, workflow_id, trigger_type)
);

CREATE INDEX idx_triggers_user_id ON triggers(user_id);
CREATE INDEX idx_triggers_workflow_id ON triggers(workflow_id);
CREATE INDEX idx_triggers_type_active ON triggers(trigger_type, is_active);
```

### 4.2 Trigger Executions Table

```sql
CREATE TABLE trigger_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trigger_id UUID NOT NULL REFERENCES triggers(id) ON DELETE CASCADE,
    event_data JSONB NOT NULL,
    workflow_execution_id VARCHAR(255), -- correlationId from workflow service
    status VARCHAR(50) NOT NULL, -- 'pending', 'success', 'failed', 'retrying'
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_trigger_executions_trigger_id ON trigger_executions(trigger_id);
CREATE INDEX idx_trigger_executions_status ON trigger_executions(status);
```

## 5. API Specifications

### 5.1 Trigger Management Endpoints

#### Create Trigger

```http
POST /api/v1/triggers
Content-Type: application/json

{
    "user_id": "user-123",
    "workflow_id": "workflow-456",
    "trigger_type": "google_calendar",
    "trigger_name": "Meeting Reminder Workflow",
    "trigger_config": {
        "calendar_id": "primary",
        "event_filters": {
            "title_contains": ["meeting", "call"],
            "attendee_count_min": 2
        }
    },
    "event_types": ["created", "updated"]
}
```

#### List User Triggers

```http
GET /api/v1/triggers?user_id=user-123&workflow_id=workflow-456
```

#### Update Trigger

```http
PUT /api/v1/triggers/{trigger_id}
```

#### Delete Trigger

```http
DELETE /api/v1/triggers/{trigger_id}
```

### 5.2 Webhook Endpoints

#### Google Calendar Webhook

```http
POST /api/v1/webhooks/google-calendar
```

### 5.3 Health Check

```http
GET /api/v1/health
```

## 6. Google Calendar Integration

### 6.1 Event Types Supported

- **Event Created**: New calendar events
- **Event Updated**: Modifications to existing events
- **Event Deleted**: Event removals
- **Event Reminder**: Pre-event notifications

### 6.2 Authentication Flow

1. Trigger service requests credentials from Auth service
2. Auth service provides OAuth2 tokens for Google Calendar API
3. Trigger service uses tokens to set up webhooks and API access
4. Token refresh handled automatically via Auth service

### 6.3 Event Detection Methods

- **Webhooks**: Primary method for real-time event detection
- **Polling**: Fallback method for webhook failures
- **Sync Token**: Efficient incremental synchronization

## 7. Workflow Execution Integration

### 7.1 Execution Request Format

```json
{
  "approval": true,
  "payload": {
    "user_dependent_fields": ["calendar_event_id", "event_title", "attendees"],
    "user_payload_template": {
      "calendar_event_id": {
        "transition_id": "transition-1",
        "value": "event-123"
      },
      "event_title": {
        "transition_id": "transition-1",
        "value": "Team Meeting"
      },
      "attendees": {
        "transition_id": "transition-1",
        "value": ["<EMAIL>", "<EMAIL>"]
      }
    }
  },
  "user_id": "user-123",
  "workflow_id": "workflow-456"
}
```

### 7.2 Response Handling

```json
{
  "correlationId": "b0bad8e3-0470-4797-af64-f1e010c484a0"
}
```

## 8. Error Handling & Retry Mechanisms

### 8.1 Retry Strategy

- Exponential backoff: 1s, 2s, 4s, 8s, 16s
- Maximum retry attempts: 5
- Dead letter queue for failed executions
- Manual retry capability via API

### 8.2 Error Categories

- **Transient Errors**: Network timeouts, rate limits
- **Authentication Errors**: Invalid/expired tokens
- **Configuration Errors**: Invalid trigger setup
- **Workflow Errors**: Workflow service unavailable

## 9. Scalability Considerations

### 9.1 Horizontal Scaling

- Stateless service design
- Database connection pooling
- Load balancer compatible

### 9.2 Performance Optimization

- Async processing with task queues
- Batch processing for multiple triggers
- Caching for frequently accessed data

### 9.3 Future Trigger Types

- Slack message triggers
- Email triggers
- GitHub webhook triggers
- Custom webhook triggers
- Scheduled/cron triggers

## 10. Monitoring & Logging

### 10.1 Metrics

- Trigger execution success/failure rates
- API response times
- Database query performance
- External service call latencies

### 10.2 Logging

- Structured JSON logging
- Correlation IDs for request tracing
- Security audit logs
- Error stack traces

## 11. Security Considerations

### 11.1 Authentication

- API key authentication for internal services
- OAuth2 integration with Auth service
- Secure credential storage

### 11.2 Data Protection

- Encryption at rest for sensitive data
- HTTPS for all API communications
- Input validation and sanitization

## 12. Sequence Diagrams

### 12.1 Trigger Setup Flow

```mermaid
sequenceDiagram
    participant UI as Chat Interface
    participant GA as Global Agent
    participant TS as Trigger Service
    participant AS as Auth Service
    participant DB as PostgreSQL
    participant GC as Google Calendar

    UI->>GA: Setup trigger for workflow
    GA->>TS: POST /api/v1/triggers
    TS->>DB: Store trigger configuration
    TS->>AS: Request Google Calendar credentials
    AS->>TS: Return OAuth2 tokens
    TS->>GC: Setup webhook subscription
    GC->>TS: Webhook confirmation
    TS->>GA: Trigger created successfully
    GA->>UI: Confirmation message
```

### 12.2 Event Detection and Workflow Execution Flow

```mermaid
sequenceDiagram
    participant GC as Google Calendar
    participant TS as Trigger Service
    participant DB as PostgreSQL
    participant WS as Workflow Service

    GC->>TS: Webhook: Event created/updated/deleted
    TS->>DB: Query matching triggers
    DB->>TS: Return trigger configurations
    TS->>DB: Fetch workflow data
    DB->>TS: Return workflow JSON
    TS->>WS: POST /api/v1/workflow-execute/execute
    WS->>TS: Return correlationId
    TS->>DB: Log execution record

    Note over TS: If execution fails
    TS->>TS: Retry with exponential backoff
    TS->>DB: Update execution status
```

### 12.3 Error Handling and Retry Flow

```mermaid
sequenceDiagram
    participant TS as Trigger Service
    participant WS as Workflow Service
    participant DB as PostgreSQL
    participant DLQ as Dead Letter Queue

    TS->>WS: Execute workflow (attempt 1)
    WS-->>TS: Error response
    TS->>DB: Log failed execution

    Note over TS: Wait 1 second
    TS->>WS: Execute workflow (attempt 2)
    WS-->>TS: Error response
    TS->>DB: Update retry count

    Note over TS: Continue retries with exponential backoff
    TS->>WS: Execute workflow (attempt 5)
    WS-->>TS: Error response
    TS->>DLQ: Send to dead letter queue
    TS->>DB: Mark as permanently failed
```

## 13. Technical Implementation Details

### 13.1 Project Structure

```
trigger-service/
├── src/
│   ├── adapters/
│   │   ├── __init__.py
│   │   ├── base.py
│   │   └── google_calendar.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes/
│   │   │   ├── triggers.py
│   │   │   ├── webhooks.py
│   │   │   └── health.py
│   │   └── middleware/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── trigger_manager.py
│   │   ├── workflow_executor.py
│   │   └── auth_client.py
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py
│   │   ├── migrations/
│   │   └── connection.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── retry.py
│   │   └── validators.py
│   └── main.py
├── tests/
├── requirements.txt
├── docker-compose.yml
├── Dockerfile
└── README.md
```

### 13.2 Key Dependencies

```python
# requirements.txt
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
google-api-python-client==2.108.0
google-auth==2.23.4
google-auth-oauthlib==1.1.0
pydantic==2.5.0
celery==5.3.4
redis==5.0.1
httpx==0.25.2
structlog==23.2.0
```

## 14. Configuration Management

### 14.1 Environment Variables

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/triggers_db

# Auth Service
AUTH_SERVICE_URL=https://auth-service.example.com
AUTH_SERVICE_API_KEY=your-api-key

# Workflow Service
WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
WORKFLOW_SERVICE_API_KEY=your-api-key

# Google Calendar
GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
```

## 15. Implementation Timeline

### Phase 1 (Weeks 1-2): Core Infrastructure

- Database schema and migrations
- Base trigger interface and manager
- API framework setup
- Authentication integration

### Phase 2 (Weeks 3-4): Google Calendar Integration

- Google Calendar adapter implementation
- Webhook handling and event processing
- Workflow execution integration
- Basic error handling

### Phase 3 (Weeks 5-6): Advanced Features & Testing

- Retry mechanisms and dead letter queue
- Comprehensive error handling
- Unit and integration tests
- Performance optimization

### Phase 4 (Week 7): Deployment & Documentation

- Docker containerization
- Deployment scripts
- API documentation
- Monitoring setup

## 16. Success Metrics

### 16.1 Performance Metrics

- Trigger detection latency: < 5 seconds
- Workflow execution success rate: > 99%
- API response time: < 200ms (95th percentile)
- System uptime: > 99.9%

### 16.2 Scalability Metrics

- Support for 10,000+ active triggers
- Handle 1,000+ events per minute
- Database query performance: < 100ms
- Memory usage: < 512MB per instance
